<template>
  <div class="memo-sidebar-container">
    <!-- 遮罩层 -->
    <div v-if="isOpen" class="sidebar-overlay" @click="handleClose"></div>
    <!-- 侧边栏 -->
    <div class="memo-sidebar" :class="{ 'sidebar-open': isOpen }">
      <div class="sidebar-header">
        <div class="title">备忘录</div>
        <div class="intimacy-display">
          <span class="intimacy-label">当前懂量：</span>
          <span class="intimacy-value">{{ intimacyScore }}</span>
        </div>
        <div class="close-btn" @click="handleClose">
          <DeleteIcon :size="20" color="var(--primary-color)" />
        </div>
      </div>
      <div class="sidebar-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <div class="error-text">{{ error }}</div>
          <div class="retry-btn" @click="fetchMemoData">重试</div>
        </div>
        <!-- 备忘录内容 -->
        <div v-else class="memo-list"> 
          <!-- 动态渲染分类 -->
          <div v-for="category in memoCategories" :key="category.title" class="memo-category">
            <div class="category-title">{{ category.title }}</div>
            <div class="memo-items">
              <div
                v-for="item in category.items"
                :key="item.label"
                class="memo-item"
                @click="handleEditMemoItem(category.title, item)"
              >
                <div class="memo-content">
                  <div class="memo-label">{{ item.label }}</div>
                  <div v-if="!isEditingItem(category.title, item)" class="memo-value">
                    {{ item.value }}
                  </div>
                  <!-- 编辑状态 -->
                  <div v-else class="memo-edit-container">
                    <!-- 事件编辑：在memo-value下方显示输入框 -->
                    <div v-if="category.title === '重要事件'" class="memo-value">
                      {{ item.value }}
                    </div>
                    <div v-if="category.title === '重要事件'" class="edit-input-wrapper">
                      <div class="voice-input-container">
                        <div
                          class="voice-btn"
                          :class="{
                            breathing:
                              isRecording &&
                              currentRecordingField === `edit-${category.title}-${item.label}`,
                          }"
                          @click.stop="
                            handleVoiceButtonClick(`edit-${category.title}-${item.label}`)
                          "
                        >
                          <img src="@/assets/icon/mic.png" alt="语音输入" />
                        </div>
                      </div>
                      <input
                        :ref="(el) => setEditInputRef(el, category.title, item.label)"
                        v-model="editInputValue"
                        type="text"
                        class="edit-input"
                        placeholder="用自然语言描述如何修改这个事件"
                        @click.stop
                      />
                    </div>
                    <!-- 属性编辑：直接编辑memo-value -->
                    <div v-else class="edit-input-wrapper">
                      <div class="voice-input-container">
                        <div
                          class="voice-btn"
                          :class="{
                            breathing:
                              isRecording &&
                              currentRecordingField === `edit-${category.title}-${item.label}`,
                          }"
                          @click.stop="
                            handleVoiceButtonClick(`edit-${category.title}-${item.label}`)
                          "
                        >
                          <img src="@/assets/icon/mic.png" alt="语音输入" />
                        </div>
                      </div>
                      <input
                        :ref="(el) => setEditInputRef(el, category.title, item.label)"
                        v-model="editInputValue"
                        type="text"
                        class="edit-input"
                        :placeholder="`编辑${item.label}`"
                        @click.stop
                      />
                    </div>
                    <!-- 编辑操作按钮 -->
                    <div class="edit-actions">
                      <button class="save-btn" @click.stop="handleSaveEdit(category.title, item)">
                        保存
                      </button>
                      <button class="cancel-btn" @click.stop="handleCancelEdit">取消</button>
                    </div>
                  </div>
                </div>
                <div v-if="!isEditingItem(category.title, item)" class="memo-actions">
                  <div class="delete-btn" @click.stop="handleDeleteMemoItem(category.title, item)">
                    <DeleteIcon :size="18" color="var(--primary-color)" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 空状态 -->
          <div v-if="memoCategories.length === 0" class="empty-state">
            <div class="empty-text">暂无备忘录信息</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 删除确认对话框 -->
  <DeleteConfirmDialog
    :visible="showDeleteDialog"
    :content="deleteDialogContent"
    :hint="deleteDialogHint"
    :is-loading="isDeleting"
    @confirm="confirmDeleteMemoItem"
    @cancel="closeDeleteDialog"
  />
</template>

<script setup lang="ts">
import { watch, ref, onMounted, nextTick, onBeforeUnmount } from 'vue';
import { showSuccessToast, showFailToast, showToast } from 'vant';
import DeleteConfirmDialog from '@/components/Common/DeleteConfirmDialog.vue';
import fetchInstance from '@/lib/fetch';
import { useUserStore } from '@/stores/user';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';
import { getMemoInfo, type IMemoCategory, type IMemoItem, type IEventItem } from '../../apis/memo';
import { getUserInfo } from '../../apis/common';
import { getIntimacy, deletePersonEvent, updateEventNatural } from '../../apis/memory';
import { updatePerson } from '../../apis/relation';
import { getStreamAsr } from '../../apis/chat';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['close']);

// 响应式数据
const loading = ref(false);
const error = ref('');
const memoCategories = ref<IMemoCategory[]>([]);
const intimacyScore = ref<number | string>('--');

// 删除相关状态
const showDeleteDialog = ref(false);
const isDeleting = ref(false);
const deleteDialogContent = ref('');
const deleteDialogHint = ref('');
const itemToDelete = ref<{
  category: string;
  item: IMemoItem;
  type: 'event' | 'attribute';
  eventId?: string;
} | null>(null);

// 用户存储
const userStore = useUserStore();

// 编辑相关状态
const editingItem = ref<{
  category: string;
  item: IMemoItem;
  type: 'event' | 'attribute';
  eventId?: string;
} | null>(null);
const editInputValue = ref('');
const editInputRefs = ref<Record<string, HTMLInputElement>>({});

// 语音录音相关状态
const isRecording = ref(false);
const currentRecordingField = ref('');
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref<ArrayBuffer | null>(null);
const voiceMessage = ref('');
const lastVoiceText = ref('');
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: number | null = null;

// 存储原始响应数据，用于删除操作
const originalResponseData = ref<{
  person: {
    person_id: string;
    canonical_name: string;
    aliases: string;
    relationships: string[];
    profile_summary: string;
    key_attributes: Record<string, string>;
    avatar: string;
    is_user: boolean;
  };
  events: { events: IEventItem[] };
} | null>(null);

// 获取懂量数据
const fetchIntimacyData = async (userId: string) => {
  try {
    console.log('🔄 [memoSidebar] 开始获取懂量数据, userId:', userId);

    const intimacyData = await getIntimacy({ user_id: userId });
    intimacyScore.value = intimacyData.intimacy_score || 0;

    console.log('✅ [memoSidebar] 懂量数据获取成功:', {
      intimacy_score: intimacyScore.value,
      level: intimacyData.level,
    });
  } catch (err) {
    console.error('❌ [memoSidebar] 获取懂量数据失败:', err);
    intimacyScore.value = '--';
  }
};

// 获取备忘录数据
const fetchMemoData = async () => {
  loading.value = true;
  error.value = '';

  try {
    // 获取用户信息以获取 mis_id
    let userId = '';

    if (userStore.userInfo?.login) {
      userId = userStore.userInfo.login;
    } else {
      // 如果 userStore 中没有用户信息，则直接调用接口获取
      const userInfo = await getUserInfo();
      userId = userInfo.login;
    }

    if (!userId) {
      error.value = '用户信息不存在';
      return;
    }

    console.log('🔄 [memoSidebar] 开始获取备忘录数据, userId:', userId);

    // 获取原始数据用于删除操作
    const rawData = await fetchInstance.fetch('/humanrelation/get_user_profile_and_events', {
      method: 'GET',
      params: {
        user_id: userId,
      },
    });
    originalResponseData.value = rawData;

    // 并行获取备忘录数据和懂量数据
    const [memoData] = await Promise.all([getMemoInfo(userId), fetchIntimacyData(userId)]);

    // 调用备忘录接口（数据处理已在API层完成）
    memoCategories.value = memoData;

    console.log('✅ [memoSidebar] 备忘录数据获取成功:', {
      categories_count: memoCategories.value.length,
      categories: memoCategories.value.map((cat) => ({
        title: cat.title,
        items_count: cat.items.length,
      })),
    });
  } catch (err) {
    console.error('❌ [memoSidebar] 获取备忘录数据失败:', err);
    error.value = '网络错误，请重试';
  } finally {
    loading.value = false;
  }
};

// 关闭侧边栏
const handleClose = () => {
  emit('close');
};

// 监听侧边栏打开状态
watch(
  () => props.isOpen,
  (newVal) => {
    if (newVal) {
      document.body.style.overflow = 'hidden';
      // 侧边栏打开时获取数据
      void fetchMemoData();
    } else {
      document.body.style.overflow = '';
    }
  },
);

// 组件挂载时如果侧边栏已经打开，则获取数据
onMounted(() => {
  if (props.isOpen) {
    void fetchMemoData();
  }
});

// 处理删除备忘录项目
const handleDeleteMemoItem = (categoryTitle: string, item: IMemoItem) => {
  // 判断是事件还是属性
  const isEvent = categoryTitle === '重要事件';

  if (isEvent) {
    // 从原始数据中找到对应的事件ID
    const events = originalResponseData.value?.events?.events || [];
    const matchingEvent = events.find((event: IEventItem) => event.description_text === item.value);

    if (matchingEvent) {
      itemToDelete.value = {
        category: categoryTitle,
        item,
        type: 'event',
        eventId: matchingEvent.event_id,
      };
      deleteDialogContent.value = `确定要删除事件 "${item.value}" 吗？`;
      deleteDialogHint.value = '删除后将无法恢复该随手记';
    } else {
      showFailToast('无法找到对应的随手记');
      return;
    }
  } else {
    // 属性删除
    itemToDelete.value = {
      category: categoryTitle,
      item,
      type: 'attribute',
    };
    deleteDialogContent.value = `确定要删除 "${item.label}" 信息吗？`;
    deleteDialogHint.value = '删除后将无法恢复该信息';
  }

  showDeleteDialog.value = true;
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
  itemToDelete.value = null;
  deleteDialogContent.value = '';
  deleteDialogHint.value = '';
};

// 确认删除备忘录项目
const confirmDeleteMemoItem = async () => {
  if (!itemToDelete.value) return;

  try {
    isDeleting.value = true;

    // 获取用户ID
    let userId = '';
    if (userStore.userInfo?.login) {
      userId = userStore.userInfo.login;
    } else {
      const userInfo = await getUserInfo();
      userId = userInfo.login;
    }

    if (itemToDelete.value.type === 'event' && itemToDelete.value.eventId) {
      // 删除事件
      const response = await deletePersonEvent({
        user_id: userId,
        event_id: itemToDelete.value.eventId,
      });

      if (response.result === 'success') {
        showSuccessToast('事件删除成功');
        // 关闭弹窗
        closeDeleteDialog();
        // 延迟1100ms后刷新数据，确保ES数据库已完成删除
        setTimeout(async () => {
          console.log('🔄 [memoSidebar] 延迟刷新数据（删除事件成功）');
          await fetchMemoData();
        }, 1100);
      } else {
        showFailToast('删除事件失败');
        closeDeleteDialog();
      }
    } else if (itemToDelete.value.type === 'attribute') {
      // 删除属性 - 通过更新person信息来实现
      const personData = originalResponseData.value?.person;
      if (personData) {
        // 复制现有的key_attributes并移除要删除的属性
        const currentAttributes = { ...personData.key_attributes };
        delete currentAttributes[itemToDelete.value.item.label];

        const response = await updatePerson(personData.person_id, {
          user_id: userId,
          canonical_name: personData.canonical_name,
          aliases: personData.aliases || '',
          relationships: personData.relationships || [],
          profile_summary: personData.profile_summary || '',
          key_attributes: currentAttributes,
          avatar: personData.avatar || '',
          is_user: personData.is_user || false,
        });

        if (response.result === 'success') {
          showSuccessToast('信息删除成功');
          // 关闭弹窗
          closeDeleteDialog();
          // 延迟1100ms后刷新数据，确保ES数据库已完成删除
          setTimeout(async () => {
            console.log('🔄 [memoSidebar] 延迟刷新数据（删除信息成功）');
            await fetchMemoData();
          }, 1100);
        } else {
          showFailToast('删除信息失败');
          closeDeleteDialog();
        }
      } else {
        showFailToast('无法获取用户信息');
        closeDeleteDialog();
      }
    }
  } catch (deleteError) {
    console.error('删除失败:', deleteError);
    showFailToast('删除失败，请重试');
    closeDeleteDialog();
  } finally {
    isDeleting.value = false;
  }
};

// 编辑相关方法
// 判断是否正在编辑某个项目
const isEditingItem = (categoryTitle: string, item: IMemoItem) => {
  return (
    editingItem.value &&
    editingItem.value.category === categoryTitle &&
    editingItem.value.item.label === item.label &&
    editingItem.value.item.value === item.value
  );
};

// 处理编辑备忘录项目
const handleEditMemoItem = (categoryTitle: string, item: IMemoItem) => {
  // 如果已经在编辑状态，不做任何操作
  if (isEditingItem(categoryTitle, item)) {
    return;
  }

  // 取消之前的编辑状态
  if (editingItem.value) {
    handleCancelEdit();
  }

  // 判断是事件还是属性
  const isEvent = categoryTitle === '重要事件';

  if (isEvent) {
    // 从原始数据中找到对应的事件ID
    const events = originalResponseData.value?.events?.events || [];
    const matchingEvent = events.find((event: IEventItem) => event.description_text === item.value);

    if (matchingEvent) {
      editingItem.value = {
        category: categoryTitle,
        item,
        type: 'event',
        eventId: matchingEvent.event_id,
      };
      editInputValue.value = ''; // 事件编辑时输入框为空，用于输入修改描述
    } else {
      showFailToast('无法找到对应的随手记');
    }
  } else {
    // 属性编辑
    editingItem.value = {
      category: categoryTitle,
      item,
      type: 'attribute',
    };
    editInputValue.value = item.value; // 属性编辑时显示当前值
  }
};

// 设置编辑输入框引用
const setEditInputRef = (el: unknown, categoryTitle: string, itemLabel: string) => {
  if (el && el instanceof HTMLInputElement) {
    const key = `${categoryTitle}-${itemLabel}`;
    editInputRefs.value[key] = el;
  }
};

// 取消编辑
const handleCancelEdit = () => {
  editingItem.value = null;
  editInputValue.value = '';
  currentRecordingField.value = '';
  // 停止录音
  if (isRecording.value) {
    void stopRecording();
  }
};

// 保存编辑
const handleSaveEdit = async (_categoryTitle: string, item: IMemoItem) => {
  if (!editingItem.value || !editInputValue.value.trim()) {
    showFailToast('请输入内容');
    return;
  }

  try {
    // 获取用户ID
    let userId = '';
    if (userStore.userInfo?.login) {
      userId = userStore.userInfo.login;
    } else {
      const userInfo = await getUserInfo();
      userId = userInfo.login;
    }

    if (editingItem.value.type === 'event' && editingItem.value.eventId) {
      // 更新事件
      const response = await updateEventNatural({
        user_id: userId,
        event_id: editingItem.value.eventId,
        update_text: editInputValue.value.trim(),
      });

      if (response && response.success) {
        showSuccessToast('事件更新成功');
        handleCancelEdit();
        // 延迟1100ms后刷新数据，确保ES数据库已完成更新
        setTimeout(async () => {
          console.log('🔄 [memoSidebar] 延迟刷新数据（更新事件成功）');
          await fetchMemoData();
        }, 1100);
      } else {
        showFailToast('更新事件失败');
      }
    } else {
      // 更新属性
      const person = originalResponseData.value?.person;
      if (person) {
        // 更新key_attributes中对应的属性
        const updatedAttributes = { ...person.key_attributes };
        updatedAttributes[item.label] = editInputValue.value.trim();

        const response = await updatePerson(person.person_id, {
          user_id: userId,
          canonical_name: person.canonical_name,
          aliases: person.aliases,
          relationships: person.relationships,
          profile_summary: person.profile_summary,
          key_attributes: updatedAttributes,
          avatar: person.avatar,
          is_user: person.is_user,
        });

        if (response.result === 'success') {
          showSuccessToast('信息更新成功');
          handleCancelEdit();
          // 延迟1100ms后刷新数据，确保ES数据库已完成更新
          setTimeout(async () => {
            console.log('🔄 [memoSidebar] 延迟刷新数据（更新信息成功）');
            await fetchMemoData();
          }, 1100);
        } else {
          showFailToast('更新信息失败');
        }
      } else {
        showFailToast('无法获取用户信息');
      }
    }
  } catch (saveError) {
    console.error('保存编辑失败:', saveError);
    showFailToast('保存失败，请重试');
  }
};

// 语音录音相关方法
// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (recorder) {
    try {
      recorder.close();
    } catch (closeError) {
      console.warn('关闭录音器时出错:', closeError);
    }
    recorder = null;
  }
};

// 初始化录音器
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  // 当录音开始时的回调
  recorder.onstart = () => {};

  // 处理录音错误的回调
  recorder.onstreamerror = () => {
    showToast('录音失败');
    void stopRecording();
  };

  // 获取可用数据的回调
  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      try {
        const streamData = await getStreamAsr({
          sessionId: sessionId.value,
          format: 'pcm',
          sampleRate: 16000,
          index: audioBufferIndex.value,
          data: data.buffer,
        });

        // 检查 full_text 并插入到光标位置
        if (
          streamData.data.full_text &&
          streamData.data.full_text.trim() !== '' &&
          streamData.data.full_text !== lastVoiceText.value
        ) {
          // 计算新增的文字部分
          const newText = streamData.data.full_text;
          const previousText = lastVoiceText.value;

          // 如果新文字包含之前的文字，只插入新增部分
          let textToInsert = newText;
          if (previousText && newText.startsWith(previousText)) {
            textToInsert = newText.slice(previousText.length);
          }

          // 在光标位置插入新文字
          if (textToInsert) {
            insertTextAtCursor(textToInsert);
          }

          lastVoiceText.value = newText;
          voiceMessage.value = newText;
        }
      } catch (asrError) {
        console.error('语音识别失败:', asrError);
      }
    }
  };
};

// 开始录音
const startRecording = async () => {
  if (isRecording.value) return;

  try {
    // 请求麦克风权限
    await navigator.mediaDevices.getUserMedia({ audio: true });

    sessionId.value = generateRandomString(16);
    audioBufferIndex.value = 0;
    lastBuffer.value = null;
    voiceMessage.value = '';
    lastVoiceText.value = '';

    // 初始化录音器
    if (!recorder) {
      initRecorder();
    }

    isRecording.value = true;

    // 开始录音
    await recorder.start();

    // 设置定时器，最多录音30秒
    timerId = window.setTimeout(() => {
      if (isRecording.value) {
        void stopRecording();
      }
    }, 30000);
  } catch (recordError) {
    console.error('开始录音失败:', recordError);
    showToast('录音功能启动失败，请检查麦克风权限');
    isRecording.value = false;
  }
};

// 结束录音
const stopRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  // 释放麦克风资源
  releaseMicrophoneResources();

  try {
    await getStreamAsr({
      sessionId: sessionId.value,
      format: 'pcm',
      sampleRate: 16000,
      index: audioBufferIndex.value * -1,
      data: null,
    });
  } catch (finalAsrError) {
    console.error('结束录音时语音识别失败:', finalAsrError);
  }

  if (voiceMessage.value) {
    console.log('📤 [memoSidebar] 语音识别完成，文字已插入到光标位置:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }

  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
  currentRecordingField.value = '';
};

// 处理语音按钮点击
const handleVoiceButtonClick = async (fieldName: string) => {
  currentRecordingField.value = fieldName;

  if (isRecording.value) {
    // 如果正在录音，则停止录音
    await stopRecording();
  } else {
    // 如果没有录音，则开始录音
    await startRecording();
  }
};

// 在光标位置插入文字的工具函数
const insertTextAtCursor = (newText: string) => {
  if (!editingItem.value) return;

  const key = `${editingItem.value.category}-${editingItem.value.item.label}`;
  const inputElement = editInputRefs.value[key];

  if (!inputElement) return;

  const start = inputElement.selectionStart || 0;
  const end = inputElement.selectionEnd || 0;
  const currentValue = editInputValue.value;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  editInputValue.value = newValue;

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 组件卸载时清理资源
onBeforeUnmount(() => {
  releaseMicrophoneResources();
  if (timerId !== null) {
    clearTimeout(timerId);
  }
});
</script>

<style lang="scss" scoped>
@import '@/styles/util.scss';
@import '@/styles/variable.scss';

.memo-sidebar-container {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
}

.memo-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  width: 55%;
  height: 100%;
  background: var(--bg-glass-popup);
  backdrop-filter: blur(20px);
  box-shadow:
    var(--shadow-strong),
    0 0 0 1px var(--border-light);
  z-index: 1001;
  transform: translateX(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto;

  &.sidebar-open {
    transform: translateX(0);
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg) var(--spacing-xl);
    background: transparent;
    border-bottom: 1px solid var(--border-light);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: var(--spacing-xl);
      right: var(--spacing-xl);
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
    }

    .title {
      font-size: var(--font-size-3xl);
      font-weight: 600;
      color: #000000;
      letter-spacing: -0.5px;
    }

    .intimacy-display {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-right: var(--spacing-md);

      .intimacy-label {
        font-size: var(--font-size-2xl);
        color: #000000;
        font-weight: 500;
      }

      .intimacy-value {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: #000000;
        transition: all 0.3s ease;
      }
    }

    .close-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-sm);
      background: transparent;
      border: 1px solid transparent;
      color: var(--text-tertiary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:active {
        transform: scale(0.95);
      }

      img {
        width: 20px;
        height: 20px;
        opacity: 0.7;
        transition: opacity 0.3s ease;
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
    background-color: transparent;
    /* 隐藏滚动条但保持可滚动 */
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .loading-text {
        font-size: 30px; // 增加4px
        color: #000000;
        animation: pulse 1.5s ease-in-out infinite;
      }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 200px;
      gap: var(--spacing-md);

      .error-text {
        font-size: 24px; // 增加4px
        color: var(--text-tertiary);
        text-align: center;
      }

      .retry-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--primary-color);
        color: white;
        border-radius: var(--border-radius-sm);
        cursor: pointer;
        font-size: 22px; // 增加4px
        transition: all 0.3s ease;

        &:active {
          transform: translateY(0);
        }
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .empty-text {
        font-size: 24px; // 增加4px
        color: var(--text-tertiary);
        text-align: center;
      }
    }

    .memo-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);

      .memo-category {
        .category-title {
          font-size: 26px; // 增加4px
          font-weight: 600;
          color: #000000;
          margin-bottom: var(--spacing-md);
          padding-bottom: var(--spacing-sm);
          border-bottom: 2px solid var(--primary-color);
          letter-spacing: -0.3px;
        }

        .memo-items {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);

          .memo-item {
            padding: var(--spacing-md);
            border-radius: var(--border-radius-md);
            background: var(--bg-glass);
            border: 1px solid var(--border-light);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: space-between;

            .memo-content {
              flex: 1;
              min-width: 0;
              overflow: hidden;
            }

            .memo-label {
              font-size: 22px; // 增加4px
              font-weight: 600;
              color: #000000; // 使用灰色替代主题色时间戳颜色
              margin-bottom: 4px;
              transition: color 0.3s ease;
            }

            .memo-value {
              font-size: 22px; // 增加4px
              font-weight: 550;
              color: var(--person-detail-context);
              line-height: 1.4;
              transition: color 0.3s ease;
            }

            .memo-actions {
              display: flex;
              align-items: center;
              margin-left: 12px;
              flex-shrink: 0;

              .delete-btn {
                width: 36px;
                height: 36px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                opacity: 0.7;

                img {
                  width: 18px;
                  height: 18px;
                  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%)
                    hue-rotate(346deg) brightness(104%) contrast(97%);
                }
              }
            }
          }
        }
      }
    }
  }
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(8px);
  z-index: 1000;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  transition: all 0.3s ease;
}

/* 添加一些微动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.memo-sidebar.sidebar-open {
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-overlay {
  animation: fadeIn 0.3s ease;
}

// 编辑相关样式
.memo-edit-container {
  width: 100%;

  .edit-input-wrapper {
    position: relative;
    margin-top: var(--spacing-sm);

    .voice-input-container {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;

      .voice-btn {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: 2px solid var(--primary-color);
        background: transparent;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &.breathing {
          animation: breathing 1.5s ease-in-out infinite;
          background: var(--primary-color-medium);
          box-shadow: 0 0 20px var(--primary-color-strong);
        }

        img {
          width: 16px;
          height: 16px;
        }
      }
    }

    .edit-input {
      width: 100%;
      padding: 18px 22px 18px 60px; // 左侧留出语音按钮空间
      border: 2px solid var(--primary-color-strong);
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 22px; // 与memo-value字体大小一致
      outline: none;
      transition: all 0.3s ease;
      min-height: 60px;
      font-family: inherit;
      box-sizing: border-box;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        border-color: var(--primary-color);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px var(--primary-color-medium);
      }
    }
  }

  .edit-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    justify-content: flex-end;

    .save-btn,
    .cancel-btn {
      flex: 1;
      padding: 16px 16px;
      border-radius: 20px;
      font-size: 22px; // 与memo-value字体大小一致
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      background: var(--bg-glass);
      backdrop-filter: blur(10px);
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .save-btn {
      color: #000000;
      border-color: var(--primary-color);


    }

    .cancel-btn {
      color: var(--text-tertiary);
      border-color: var(--border-glass);


    }
  }
}

// 语音按钮呼吸动画
@keyframes breathing {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px var(--primary-color-strong);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px var(--primary-color);
  }
}
</style>
