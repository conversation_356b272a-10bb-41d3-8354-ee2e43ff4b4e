<template>
  <div class="weather-raw-data">
    <!-- 地点选择器 -->
    <div v-if="locations.length > 1" class="location-selector">
      <div class="location-tabs">
        <button
          v-for="location in locations"
          :key="location"
          :class="['location-tab', { active: selectedLocation === location }]"
          @click="selectLocation(location)"
        >
          {{ location }}
        </button>
      </div>
    </div>

    <!-- 天气原始信息展示 -->
    <div v-if="currentWeatherData" class="weather-info">
      <!-- 天气卡片 - 备忘录风格 -->
      <div class="weather-card">
        <!-- 第一行：地点天气情况 -->
        <div class="weather-title">
          {{ currentWeatherData.location.name }}天气情况
        </div>

        <!-- 第二行：主要天气信息 -->
        <div class="weather-summary">
          {{ currentWeatherData.weather.weather }}
          气温{{ currentWeatherData.weather.temperature }}℃
          体感{{ currentWeatherData.weather.feelsLike }}℃
          湿度{{ currentWeatherData.weather.humidity }}
          {{ currentWeatherData.weather.windDirection }}
          {{ currentWeatherData.weather.windPower }}
        </div>


      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="no-data">
      <div class="no-data-text">暂无天气数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { IWeatherDataItem } from '@/apis/memory';

// Props
interface IProps {
  weatherData: IWeatherDataItem[];
}

const props = defineProps<IProps>();

// 响应式数据
const selectedLocation = ref<string>('');

// 计算属性
const locations = computed(() => {
  return props.weatherData.map(item => item.location.name);
});

const currentWeatherData = computed(() => {
  console.log('🔍 [WeatherRawData] 计算当前天气数据:', {
    selectedLocation: selectedLocation.value,
    availableLocations: props.weatherData.map(item => item.location.name)
  });

  if (!selectedLocation.value || !props.weatherData.length) {
    const result = props.weatherData[0] || null;
    console.log('🎯 [WeatherRawData] 返回默认数据:', result?.location?.name);
    return result;
  }

  const result = props.weatherData.find(item => item.location.name === selectedLocation.value) || props.weatherData[0];
  console.log('🎯 [WeatherRawData] 返回匹配数据:', result?.location?.name);
  return result;
});

// 方法
const selectLocation = (location: string) => {
  console.log('🌍 [WeatherRawData] 切换地点:', location);
  selectedLocation.value = location;
};

// 监听数据变化，自动选择第一个地点
watch(
  () => props.weatherData,
  (newData) => {
    console.log('📊 [WeatherRawData] 天气数据更新:', newData.map(item => item.location.name));
    if (newData.length > 0) {
      // 检查当前选中的地点是否在新数据中存在
      const currentLocationExists = newData.some(item => item.location.name === selectedLocation.value);

      // 如果当前选中的地点不存在，或者还没有选中地点，则选择第一个
      if (!selectedLocation.value || !currentLocationExists) {
        selectedLocation.value = newData[0].location.name;
        console.log('🎯 [WeatherRawData] 自动选择地点:', selectedLocation.value);
      }
    }
  },
  { immediate: true }
);

// 监听当前天气数据变化
watch(
  currentWeatherData,
  (newData) => {
    if (newData) {
      console.log('🌤️ [WeatherRawData] 当前天气数据更新:', {
        location: newData.location.name,
        weather: newData.weather.weather,
        temperature: newData.weather.temperature
      });
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.weather-raw-data {
  background: var(--bg-glass);
  border-radius: 20px;
  box-shadow: var(--shadow-soft);
  padding: 20px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-glass);
}

// 地点选择器 - 使用项目主题样式
.location-selector {
  margin-bottom: 24px;

  .location-tabs {
    display: flex;
    gap: 14px; // 参考TopicSection的section-actions gap
    flex-wrap: wrap;
    justify-content: flex-start;

    .location-tab {
      // 参考TopicSection的refresh-btn样式
      padding: 14px 24px;
      border: 2px solid var(--primary-color);
      background: transparent;
      border-radius: var(--border-radius-full);
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 20px; // H5移动端适配
      font-weight: 600;
      color: var(--text-primary);
      min-width: 90px;
      text-align: center;
      backdrop-filter: blur(10px);

      &.active {
        background: var(--primary-color);
        color: var(--on-primary-text);
        box-shadow: var(--shadow-accent);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 天气信息
.weather-info {
  .weather-header {
    margin-bottom: 24px;
    text-align: center;

    .location-name {
      font-size: 28px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .location-source {
      font-size: 16px;
      color: #666;
    }
  }

  .weather-details {
    .main-weather {
      text-align: center;
      margin-bottom: 32px;
      padding: 24px;
      background: linear-gradient(135deg, rgba(0, 188, 212, 0.1), rgba(0, 188, 212, 0.05));
      border-radius: 12px;

      .temperature-section {
        .current-temp {
          font-size: 48px;
          font-weight: 700;
          color: #00bcd4;
          margin-bottom: 8px;
        }

        .feels-like {
          font-size: 18px;
          color: #666;
          margin-bottom: 12px;
        }

        .weather-desc {
          font-size: 24px;
          color: #333;
          font-weight: 500;
        }
      }
    }

    .weather-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
      margin-bottom: 24px;

      .weather-item {
        background: rgba(0, 188, 212, 0.05);
        padding: 16px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid rgba(0, 188, 212, 0.1);

        .weather-label {
          font-size: 16px;
          color: #666;
          margin-bottom: 8px;
        }

        .weather-value {
          font-size: 20px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .update-time {
      text-align: center;
      font-size: 16px;
      color: #999;
      font-style: italic;
    }
  }
}

// 无数据状态
.no-data {
  text-align: center;
  padding: 40px;

  .no-data-text {
    font-size: 20px;
    color: #999;
  }
}

// 天气信息卡片 - 参考TopicSection样式
.weather-info {
  .weather-card {
    // 使用项目主题背景和边框
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border-left: 4px solid var(--accent-color);
    box-shadow: var(--shadow-accent);
    border-radius: var(--border-radius-lg);
    padding: 22px 22px 22px 28px; // 参考TopicSection的padding
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;

    .weather-title {
      color: var(--text-primary);
      font-size: 34px; // 参考TopicSection的section-title
      font-weight: 600;
      margin-bottom: 16px;
      line-height: 1.3;
    }

    .weather-summary {
      color: var(--person-detail-context);
      font-size: 32px; // 参考TopicSection的topic-text
      font-weight: 450;
      line-height: 1.4;
      background: var(--primary-color-light);
      border: 2px solid var(--border-accent);
      border-radius: var(--border-radius-lg);
      padding: 20px;
      transition: all 0.3s ease;
    }
  }
}

// 无数据状态 - 使用项目主题样式
.no-data {
  text-align: center;
  padding: 40px 20px;
  background: var(--bg-glass);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  border: 1px dashed var(--border-accent);

  .no-data-text {
    font-size: 30px; // 参考TopicSection的empty-topic
    color: var(--text-primary);
    font-style: italic;
    line-height: 1.6;
  }
}
</style>
